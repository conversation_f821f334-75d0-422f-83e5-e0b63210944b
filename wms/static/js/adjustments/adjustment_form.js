document.addEventListener('DOMContentLoaded', function() {
  const adjustmentToSelect = document.getElementById('id_deliver_to');
  const formsetContainer = document.querySelector('#adjustment_form');
  const DEFAULT_PLACEHOLDER = '---------';

  // Function to update batch options based on a selected item
  function updateBatchOptions(itemSelect, batchSelect, selectedBatchNo) {

    // Clear existing options
    batchSelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;

    // Enable the batch select
    batchSelect.disabled = false;

    let selectedItem = $(itemSelect).select2('data')[0];

    // Add batch options
    selectedItem.batches.forEach(batch => {
      const option = document.createElement('option');
      option.value = batch.batch_no;
      option.textContent = batch.batch_no;
      option.dataset.stocks = JSON.stringify(batch.stocks);
      batchSelect.appendChild(option);
    });

    if (selectedBatchNo) {
      const batchExists = Array.from(selectedItem.batches).some(opt => opt.batch_no === selectedBatchNo);
      if (!batchExists) {
        const option = document.createElement('option');
        option.value = selectedBatchNo;
        option.textContent = selectedBatchNo;
        option.dataset.stocks = JSON.stringify([]);
        batchSelect.appendChild(option);
      }
    }

    if (selectedBatchNo) {
      const batchHiddenInput = itemSelect.closest('tr').querySelector('input[name$="-batch_hidden"]');
      $(batchSelect).val(selectedBatchNo);
      batchHiddenInput.value = selectedBatchNo;
    }
    // Trigger change event to refresh Select2
    $(batchSelect).trigger('change');
  }

  // Function to update expiry date options based on selected batch
  function updateExpiryOptions(batchSelect, expirySelect, expiry_date) {
    // Clear existing options
    expirySelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;

    expirySelect.disabled = false;

    // Get the selected batch data
    const selectedOption = batchSelect.options[batchSelect.selectedIndex];

    // Parse the stocks data
    const stocks = JSON.parse(selectedOption.dataset?.stocks || '[]');

    stocks.forEach(stock => {
      const option = document.createElement('option');
      option.value = stock.expiry_date;
      option.textContent = stock.expiry_date;
      option.dataset.availableBalance = stock.available_balance;
      expirySelect.appendChild(option);
    });

    if (expiry_date) {
      const expiryExists = Array.from(stocks).some(stock => stock.expiry_date === expiry_date);
      if (!expiryExists) {
        const option = document.createElement('option');
        option.value = expiry_date;
        option.textContent = expiry_date;
        expirySelect.appendChild(option);
      }
    }

    const expiryHiddenInput = batchSelect.closest('tr').querySelector('input[name$="-expiry_hidden"]');
    if (expiry_date) {
      $(expirySelect).val(expiry_date);
      expiryHiddenInput.value = expiry_date;
    } else if (stocks.length > 0) {
      expirySelect.value = stocks[0].expiry_date;
      expiryHiddenInput.value = stocks[0].expiry_date;
    }

    $(expirySelect).trigger('change');
    const selectEvent = new Event('select2:select', {bubbles: true});
    expirySelect.dispatchEvent(selectEvent);
  }

  // Function to update UOM based on selected item
  function updateUom(itemSelect, uomInput) {
    // Get the selected item data
    const selectedItem = $(itemSelect).select2('data')[0];

    if (!selectedItem || !selectedItem.uom) {
      // Clear and disable the UOM input
      uomInput.value = '';
      uomInput.disabled = true;
      return;
    }

    // Set the UOM value and ensure it's disabled (read-only)
    uomInput.value = selectedItem.uom;
    uomInput.disabled = true;
  }

  // Function to update item_id based on selected item
  function updateItemId(itemSelect, itemIdInput) {
    // Get the selected item data
    const selectedItem = $(itemSelect).select2('data')[0];

    if (!selectedItem || !selectedItem.id) {
      // Clear the item_id input
      itemIdInput.value = '';
      return;
    }

    // Set the item_id value (the item select2 data returns item.id as the id)
    itemIdInput.value = selectedItem.id;
  }

  // Function to update balance field with available balance
  function updateBalance(batchInput, expiryInput, balanceInput) {
    // Get the selected batch option
    const selectedBatchOption = batchInput.options[batchInput.selectedIndex];

    if (!selectedBatchOption || !selectedBatchOption.dataset.stocks) {
      balanceInput.value = '';
      return;
    }

    // Parse the stocks data
    const stocks = JSON.parse(selectedBatchOption.dataset.stocks);

    // Find the stock with matching expiry date
    const selectedExpiryDate = expiryInput.value;
    const matchingStock = stocks.find(stock => stock.expiry_date === selectedExpiryDate);
    if (!matchingStock || !matchingStock.available_balance) {
      balanceInput.value = '0';
      return;
    }

    // Set the balance field to the available balance
    balanceInput.value = matchingStock.available_balance;

    // Make sure the balance field is disabled (read-only)
    balanceInput.disabled = true;
  }

  // Function to reset dependent fields
  function resetDependentFields(row) {
    const batchSelect = row.querySelector('.adjustment-item-batch_no');
    const expirySelect = row.querySelector('.adjustment-item-expiry_date');
    const uomInput = row.querySelector('.adjustment-item-uom');
    const balanceInput = row.querySelector('.adjustment-item-balance');
    const itemIdInput = row.querySelector('input[name$="-item_id"]');
    const batchHiddenInput = row.querySelector('input[name$="-batch_hidden"]');
    const expiryHiddenInput = row.querySelector('input[name$="-expiry_hidden"]');
    const uomIdInput = row.querySelector('input[name$="-uom_id"]');

    if (batchSelect) {
      batchSelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;
      batchSelect.disabled = true;
      $(batchSelect).val(null).trigger('change');
    }

    if (expirySelect) {
      expirySelect.innerHTML = `<option value="">${DEFAULT_PLACEHOLDER}</option>`;
      expirySelect.disabled = true;
      $(expirySelect).val(null).trigger('change');
    }

    if (uomInput) {
      uomInput.value = '';
      uomInput.disabled = true;
    }

    if (balanceInput) {
      balanceInput.value = '';
      balanceInput.disabled = true;
    }

    // Reset hidden inputs
    if (itemIdInput) {
      itemIdInput.value = '';
    }

    if (batchHiddenInput) {
      batchHiddenInput.value = '';
    }

    if (expiryHiddenInput) {
      expiryHiddenInput.value = '';
    }

    if (uomIdInput) {
      uomIdInput.value = '';
    }
  }

  // // --- Event Listener for Warehouse Change ---
  if (adjustmentToSelect) {
    // Function to handle initial form load with existing values
    async function initializeExistingValues(row) {
      const itemSelect = row.querySelector('.adjustment-item-item');
      const batchSelect = row.querySelector('.adjustment-item-batch_no');
      const expirySelect = row.querySelector('.adjustment-item-expiry_date');
      const itemIdInput = row.querySelector('input[name$="-item_id"]');
      const batchHiddenInput = row.querySelector('input[name$="-batch_hidden"]');
      const expiryHiddenInput = row.querySelector('input[name$="-expiry_hidden"]');

      // Check for existing values in visible fields or hidden fields
      const existingBatchNo = batchSelect && $(batchSelect).val() ? $(batchSelect).val() :
          (batchHiddenInput ? batchHiddenInput.value : null);
      const existingExpiryDate = expirySelect && expirySelect.value ? expirySelect.value :
          (expiryHiddenInput ? expiryHiddenInput.value : null);

      if (!itemSelect || !itemIdInput) return;

      // If we have a stored item_id, fetch the item data
      const itemId = itemIdInput.value;

      if (itemId) {
        try {
          // Wait for Select2 initialization
          await new Promise(resolve => setTimeout(resolve, 300));

          // Set the item Select2 value
          const itemUrl = itemSelect.getAttribute('data-api-url');

          // Fetch item data from API
          const response = await fetch(`${itemUrl}${itemId}/`);
          const itemData = await response.json();

          if (itemData && Object.keys(itemData).length) {
            // Create the Select2 option data
            const itemOption = {
              id: itemData.id,
              text: itemData.name,
              batches: itemData.batches,
              uom: itemData.uom,
              uom_id: itemData.uom_id,
            };
            $(itemSelect).select2('trigger', 'select', {
              data: itemOption,
            });

            // Wait for item select to update
            await new Promise(resolve => setTimeout(resolve, 50));

            // Update batch options
            if (batchSelect && itemData.batches) {
              updateBatchOptions(itemSelect, batchSelect, existingBatchNo);

              // Update expiry options and select the correct one
              if (expirySelect && existingExpiryDate) {
                updateExpiryOptions(batchSelect, expirySelect, existingExpiryDate);
              }
            }
          }
        } catch (error) {
          console.error('Error initializing existing values:', error);
        }
      }
    }

    // --- Initial load ---
    if (adjustmentToSelect.value) {
      formsetContainer.querySelectorAll('.adjustment-item-item').
          forEach(itemSelect => {
            const row = itemSelect.closest('tr');
            if (row) {
              // updateItemSelectApiUrl(itemSelect, warehouseId);
              // Only initialize if we have existing values
              if (row.querySelector('input[name$="-item_id"]')?.value) {
                initializeExistingValues(row);
              }
            }
          });
    } else {
      formsetContainer.querySelectorAll('.adjustment-item-item').
          forEach(itemSelect => {
            itemSelect.disabled = false;
            const row = itemSelect.closest('tr');
            if (row) {
              resetDependentFields(row);
            }
          });
    }
  }

  // --- Event Delegation for Item, Batch, and Expiry Changes ---
  if (formsetContainer) {
    // Item select events
    $(formsetContainer).on('select2:select select2:clear', '.adjustment-item-item', function(e) {
      const row = this.closest('tr');
      if (row) {
        let event_this = this;
        resetDependentFields(row);

        if (e.type === 'select2:select') {
          const batchInput = row.querySelector('.adjustment-item-batch_no');
          const uomInput = row.querySelector('.adjustment-item-uom');
          const itemIdInput = row.querySelector('input[name$="-item_id"]');
          const uomIdInput = row.querySelector('input[name$="-uom_id"]');

          if (batchInput) {
            updateBatchOptions(event_this, batchInput);
          }

          // set the uomId hidden Field
          if (uomIdInput) {
            uomIdInput.value = $(event_this).select2('data')[0].uom_id;
          }

          if (uomInput) {
            updateUom(event_this, uomInput);
          }


          // set the itemId hidden Field
          if (itemIdInput) {
            updateItemId(event_this, itemIdInput);
          }
        }
      }
    });

    // Batch select events
    $(formsetContainer).on('change', '.adjustment-item-batch_no', function(e) {
      const row = this.closest('tr');
      if (row) {
        const expirySelect = row.querySelector('.adjustment-item-expiry_date');
        const balanceInput = row.querySelector('.adjustment-item-balance');
        const batchHiddenInput = row.querySelector('input[name$="-batch_hidden"]');
        const expiryHiddenInput = row.querySelector('input[name$="-expiry_hidden"]');

        // Reset expiry-related fields
        if (expirySelect) {
          expirySelect.value = '';
          expirySelect.disabled = true;
        }

        if (balanceInput) {
          balanceInput.value = '';
          balanceInput.disabled = true;
        }

        // Reset expiry hidden input
        if (expiryHiddenInput) {
          expiryHiddenInput.value = '';
        }

        // If this is a select event (not clear) and the value is not empty, update the expiry options
        if (this.value) {
          // Set the batch hidden input value
          if (batchHiddenInput) {
            batchHiddenInput.value = this.value;
          }

          if (expirySelect) {
            updateExpiryOptions(this, expirySelect);
          }
        } else {
          // Clear batch hidden input if batch is cleared
          if (batchHiddenInput) {
            batchHiddenInput.value = '';
          }
        }
      }
    });

    // Expiry date input events
    $(formsetContainer).on('change', '.adjustment-item-expiry_date', function(e) {
      const row = this.closest('tr');
      if (row) {
        const batchInput = row.querySelector('.adjustment-item-batch_no');
        const balanceInput = row.querySelector('.adjustment-item-balance');
        const expiryHiddenInput = row.querySelector('input[name$="-expiry_hidden"]');

        // Reset balance field
        if (balanceInput) {
          balanceInput.value = '';
          balanceInput.disabled = true;
        }

        // If the expiry date is not empty, update the hidden field and balance
        if (this.value) {
          // Set the expiry hidden input value
          if (expiryHiddenInput) {
            expiryHiddenInput.value = this.value;
          }

          // Update balance if batch is also selected
          if (batchInput && batchInput.value && balanceInput) {
            updateBalance(batchInput, this, balanceInput);
          }
        } else {
          // Clear expiry hidden input if expiry is cleared
          if (expiryHiddenInput) {
            expiryHiddenInput.value = '';
          }
        }
      }
    });
  }
});
