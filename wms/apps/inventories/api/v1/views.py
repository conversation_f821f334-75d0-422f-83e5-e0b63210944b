from django.db import models
from django.core.cache import cache
from django.conf import settings
from rest_framework import viewsets, filters
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from wms.cores.views import Select2Pagination

from wms.apps.inventories.models import Item, Stock
from .serializers import ItemSerializer, ItemDetailSerializer


class ItemViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Item objects in the Warehouse Management System.
    """
    pagination_class = Select2Pagination
    serializer_class = ItemDetailSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['code', 'name', 'item_type', 'status', 'uom', 'manage_type', 'consignor']
    search_fields = ['code', 'name', 'brand', 'sku', 'barcode']
    ordering_fields = ['code', 'name', 'sort_order', 'created']
    ordering = ['sort_order']

    def get_queryset(self):
        """
        Optimized queryset with proper prefetching for different endpoints.
        """
        # Base queryset with essential relationships
        queryset = Item.objects.select_related('uom', 'consignor')

        # For detail views (retrieve and custom detail action), prefetch stock data
        if self.action in ['retrieve', 'detail']:
            # Use optimized stock queryset with pre-calculated balances
            optimized_stock_queryset = Stock.objects.with_calculated_balances().select_related(
                'item', 'item__uom', 'warehouse'
            ).order_by('batch_no', 'expiry_date')

            queryset = queryset.prefetch_related(
                models.Prefetch('stock_set', queryset=optimized_stock_queryset),
                'categories',
                'outbound_uom_display_conversions'
            )
        else:
            # For list views, only prefetch what's needed
            queryset = queryset.prefetch_related('categories')

        return queryset

    def get_serializer_class(self):
        """
        Use different serializers for different actions to optimize performance.
        """
        if self.action == 'list':
            return ItemSerializer
        return ItemDetailSerializer

    def retrieve(self, request, *args, **kwargs):
        """
        Optimized retrieve method with caching for item details.
        """
        instance = self.get_object()

        # Generate cache key based on item ID and last modified time
        cache_key = f"item_detail_{instance.pk}_{instance.modified.timestamp()}"

        # Try to get cached response
        cached_data = cache.get(cache_key)
        if cached_data is not None:
            return Response(cached_data)

        # If not cached, serialize and cache the result
        serializer = self.get_serializer(instance)
        data = serializer.data

        # Cache for 15 minutes (adjust based on your needs)
        cache.set(cache_key, data, self.get_cache_timeout())

        return Response(data)

    def get_cache_timeout(self):
        """
        Get cache timeout from settings or use default.
        """
        return getattr(settings, 'CACHE_TTL', 300)  # Use CACHE_TTL from settings


